// Multi-Therapist Conversation Orchestrator for Comparative Study
import { v4 as uuidv4 } from 'uuid';
import { OpenAIService } from './openai.js';
import { MultiTherapistAgentService } from './agents/multi-therapist.js';
import { PatientAgentService } from './agents/patient.js';
import { CBTEvaluationObserverService } from './agents/cbt-evaluation-observer.js';
import { 
  MultiTherapistConversationContext, 
  ConversationContext,
  MultiTherapistResponse,
  TherapistPersonaResponse,
  PatientResponse,
  WebSocketClient,
  CBTEvaluationRequest,
  ComparativeCBTEvaluationResult,
  TherapistPersonaType
} from '../types/index.js';
import { ConversationConfig, defaultConversationConfig } from '../config/conversation.js';

export interface MultiTherapistConversationMessage {
  id: string;
  conversationId: string;
  sender: 'patient' | 'therapist';
  content: string;
  thinking?: string;
  metadata?: any;
  timestamp: string;
  // For therapist messages, include persona information
  personaType?: TherapistPersonaType;
  personaName?: string;
}

export class MultiTherapistConversationOrchestrator {
  private openaiService: OpenAIService;
  private multiTherapistAgent: MultiTherapistAgentService;
  private patientAgent: PatientAgentService;
  private evaluationObserver: CBTEvaluationObserverService;
  private config: ConversationConfig;
  private context: MultiTherapistConversationContext;
  private clients: WebSocketClient[] = [];
  private isActive: boolean = false;
  private currentTurn: 'therapist' | 'patient' = 'therapist';

  constructor(
    conversationId: string,
    config: ConversationConfig,
    personaId?: string
  ) {
    this.openaiService = new OpenAIService();
    this.multiTherapistAgent = new MultiTherapistAgentService(this.openaiService);
    this.patientAgent = new PatientAgentService(this.openaiService, config.patient, personaId);
    this.evaluationObserver = new CBTEvaluationObserverService(this.openaiService);
    this.config = config;

    // Initialize multi-therapist conversation context
    this.context = {
      id: conversationId,
      patientMessages: [],
      therapistConversations: {
        cbtOnly: this.createEmptyConversationContext(conversationId + '-cbt-only'),
        miFixedPretreatment: this.createEmptyConversationContext(conversationId + '-mi-fixed'),
        dynamicAdaptive: this.createEmptyConversationContext(conversationId + '-dynamic')
      },
      currentTurn: 0,
      maxTurns: config.conversation.maxTurns,
      status: 'active',
      studyMetadata: {
        startTime: new Date().toISOString(),
        patientPersonaId: personaId || 'default',
        studyConfiguration: {
          readinessThresholds: config.therapist.therapeuticApproach.readinessThresholds,
          allowDynamicSwitching: config.therapist.therapeuticApproach.approachPreferences.allowApproachSwitching
        }
      }
    };
  }

  private createEmptyConversationContext(id: string): ConversationContext {
    return {
      id,
      messages: [],
      currentTurn: 0,
      maxTurns: this.config.conversation.maxTurns,
      status: 'active'
    };
  }

  /**
   * Start the multi-therapist conversation
   */
  async startConversation(): Promise<void> {
    console.log('🚀 Starting multi-therapist conversation...');
    
    if (this.isActive) {
      console.warn('⚠️ Conversation is already active');
      return;
    }

    this.isActive = true;
    this.context.status = 'active';

    // Broadcast conversation started event
    this.broadcastToClients({
      type: 'multi_conversation_started',
      data: {
        conversationId: this.context.id,
        studyMetadata: this.context.studyMetadata
      },
      timestamp: new Date().toISOString()
    });

    // Start with therapist greetings
    await this.processMultiTherapistTurn();
  }

  /**
   * Process multi-therapist turn (all three personas respond)
   */
  private async processMultiTherapistTurn(): Promise<void> {
    console.log('👩‍⚕️ Processing multi-therapist turn...');

    try {
      let multiResponse: MultiTherapistResponse;

      if (this.context.currentTurn === 0) {
        // Initial greetings from all personas
        multiResponse = await this.multiTherapistAgent.generateInitialGreetings();
      } else {
        // Responses to patient message
        const lastPatientMessage = this.getLastPatientMessage();
        if (!lastPatientMessage) {
          console.error('❌ No patient message found for therapist response');
          return;
        }
        multiResponse = await this.multiTherapistAgent.generateMultiTherapistResponse(
          lastPatientMessage.content,
          this.context
        );
      }

      // Create and store messages for each persona
      const therapistMessages = this.createTherapistMessages(multiResponse);
      
      // Add messages to respective conversation contexts
      therapistMessages.forEach(msg => {
        const personaKey = this.getPersonaKey(msg.personaType!);
        this.context.therapistConversations[personaKey].messages.push({
          id: msg.id,
          conversationId: msg.conversationId,
          sender: 'therapist',
          content: msg.content,
          thinking: msg.thinking || '',
          metadata: msg.metadata || { confidence: 0, processingTime: 0 },
          timestamp: msg.timestamp
        });
        this.context.therapistConversations[personaKey].currentTurn++;
      });

      this.context.currentTurn++;

      // Broadcast multi-therapist messages
      this.broadcastMultiTherapistMessage(multiResponse, therapistMessages);

      // Schedule patient response if conversation should continue
      if (this.shouldContinueConversation()) {
        this.currentTurn = 'patient';
        setTimeout(() => {
          this.processPatientTurn();
        }, this.config.conversation.responseDelay.patient);
      } else {
        await this.endConversation();
      }

    } catch (error) {
      console.error('❌ Error processing multi-therapist turn:', error);
      this.handleError('Failed to generate multi-therapist response');
    }
  }

  /**
   * Process patient turn (responds to one of the therapist messages)
   */
  private async processPatientTurn(): Promise<void> {
    console.log('👤 Processing patient turn...');

    try {
      // For simplicity, patient responds to the dynamic adaptive therapist
      // In a real implementation, you might want to randomize or use other logic
      const lastDynamicMessage = this.getLastTherapistMessage('dynamicAdaptive');
      if (!lastDynamicMessage) {
        console.error('❌ No therapist message found for patient response');
        return;
      }

      const response = await this.patientAgent.generateResponse(
        lastDynamicMessage.content, 
        this.context.therapistConversations.dynamicAdaptive
      );

      // Create patient message
      const patientMessage = this.createPatientMessage(response);
      
      // Add to patient messages and all therapist conversation contexts
      this.context.patientMessages.push({
        id: patientMessage.id,
        conversationId: patientMessage.conversationId,
        content: patientMessage.content,
        timestamp: patientMessage.timestamp
      });

      // Add patient message to all therapist conversation contexts
      Object.values(this.context.therapistConversations).forEach(ctx => {
        ctx.messages.push({
          id: patientMessage.id,
          conversationId: ctx.id,
          sender: 'patient',
          content: patientMessage.content,
          thinking: patientMessage.thinking || '',
          metadata: patientMessage.metadata || { confidence: 0, processingTime: 0 },
          timestamp: patientMessage.timestamp
        });
        ctx.currentTurn++;
      });

      // Broadcast patient message
      this.broadcastPatientMessage(patientMessage);

      // Schedule multi-therapist response if conversation should continue
      if (this.shouldContinueConversation()) {
        this.currentTurn = 'therapist';
        setTimeout(() => {
          this.processMultiTherapistTurn();
        }, this.config.conversation.responseDelay.therapist);
      } else {
        await this.endConversation();
      }

    } catch (error) {
      console.error('❌ Error processing patient turn:', error);
      this.handleError('Failed to generate patient response');
    }
  }

  private createTherapistMessages(multiResponse: MultiTherapistResponse): MultiTherapistConversationMessage[] {
    const timestamp = new Date().toISOString();
    
    return [
      {
        id: uuidv4(),
        conversationId: this.context.id,
        sender: 'therapist',
        content: multiResponse.cbtOnly.message,
        thinking: multiResponse.cbtOnly.thinking,
        metadata: multiResponse.cbtOnly.metadata,
        timestamp,
        personaType: 'cbt-only',
        personaName: multiResponse.cbtOnly.personaName
      },
      {
        id: uuidv4(),
        conversationId: this.context.id,
        sender: 'therapist',
        content: multiResponse.miFixedPretreatment.message,
        thinking: multiResponse.miFixedPretreatment.thinking,
        metadata: multiResponse.miFixedPretreatment.metadata,
        timestamp,
        personaType: 'mi-fixed-pretreatment',
        personaName: multiResponse.miFixedPretreatment.personaName
      },
      {
        id: uuidv4(),
        conversationId: this.context.id,
        sender: 'therapist',
        content: multiResponse.dynamicAdaptive.message,
        thinking: multiResponse.dynamicAdaptive.thinking,
        metadata: multiResponse.dynamicAdaptive.metadata,
        timestamp,
        personaType: 'dynamic-adaptive',
        personaName: multiResponse.dynamicAdaptive.personaName
      }
    ];
  }

  private createPatientMessage(response: PatientResponse): MultiTherapistConversationMessage {
    return {
      id: uuidv4(),
      conversationId: this.context.id,
      sender: 'patient',
      content: response.message,
      thinking: response.thinking,
      metadata: response.metadata,
      timestamp: new Date().toISOString()
    };
  }

  private getPersonaKey(personaType: TherapistPersonaType): 'cbtOnly' | 'miFixedPretreatment' | 'dynamicAdaptive' {
    switch (personaType) {
      case 'cbt-only': return 'cbtOnly';
      case 'mi-fixed-pretreatment': return 'miFixedPretreatment';
      case 'dynamic-adaptive': return 'dynamicAdaptive';
      default: throw new Error(`Unknown persona type: ${personaType}`);
    }
  }

  private getLastPatientMessage() {
    return this.context.patientMessages[this.context.patientMessages.length - 1];
  }

  private getLastTherapistMessage(personaKey: 'cbtOnly' | 'miFixedPretreatment' | 'dynamicAdaptive') {
    const messages = this.context.therapistConversations[personaKey].messages;
    return messages.filter(m => m.sender === 'therapist').pop();
  }

  private shouldContinueConversation(): boolean {
    return this.context.currentTurn < this.context.maxTurns && this.isActive;
  }

  private async endConversation(): Promise<void> {
    console.log('🏁 Ending multi-therapist conversation...');
    
    this.isActive = false;
    this.context.status = 'completed';

    // Broadcast conversation ended
    this.broadcastToClients({
      type: 'multi_conversation_ended',
      data: {
        conversationId: this.context.id,
        totalTurns: this.context.currentTurn,
        studyMetadata: this.context.studyMetadata
      },
      timestamp: new Date().toISOString()
    });

    // Trigger comparative evaluation
    await this.performComparativeEvaluation();
  }

  private async performComparativeEvaluation(): Promise<void> {
    console.log('📊 Performing comparative CBT evaluation...');
    
    try {
      // Create evaluation requests for each persona
      const evaluationRequests = Object.entries(this.context.therapistConversations).map(([key, ctx]) => ({
        conversationId: ctx.id,
        messages: ctx.messages.map(m => ({
          sender: m.sender,
          content: m.content,
          timestamp: m.timestamp
        })),
        sessionMetadata: {
          duration: Math.round((Date.now() - new Date(this.context.studyMetadata.startTime).getTime()) / 60000),
          patientPersona: this.context.studyMetadata.patientPersonaId,
          therapeuticApproaches: [key]
        }
      }));

      // Perform evaluations in parallel
      const evaluations = await Promise.all(
        evaluationRequests.map(req => this.evaluationObserver.evaluateConversation(req))
      );

      // Create comparative evaluation result
      const comparativeResult: ComparativeCBTEvaluationResult = {
        id: uuidv4(),
        conversationId: this.context.id,
        evaluationTimestamp: new Date().toISOString(),
        evaluations: {
          cbtOnly: evaluations[0],
          miFixedPretreatment: evaluations[1],
          dynamicAdaptive: evaluations[2]
        },
        comparison: this.generateComparison(evaluations),
        studyMetadata: {
          patientPersonaId: this.context.studyMetadata.patientPersonaId,
          sessionDuration: Math.round((Date.now() - new Date(this.context.studyMetadata.startTime).getTime()) / 60000),
          totalPatientMessages: this.context.patientMessages.length,
          readinessScoreProgression: {
            initial: 5, // Would be calculated from actual data
            final: 7,   // Would be calculated from actual data
            average: 6  // Would be calculated from actual data
          }
        }
      };

      // Broadcast comparative evaluation result
      this.broadcastToClients({
        type: 'comparative_evaluation_complete',
        data: comparativeResult,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error performing comparative evaluation:', error);
    }
  }

  private generateComparison(evaluations: any[]) {
    const scores = evaluations.map(e => e.overallScore);
    const bestIndex = scores.indexOf(Math.max(...scores));
    const personaTypes: TherapistPersonaType[] = ['cbt-only', 'mi-fixed-pretreatment', 'dynamic-adaptive'];

    return {
      bestPerforming: personaTypes[bestIndex],
      scoreComparison: {
        cbtOnly: scores[0],
        miFixedPretreatment: scores[1],
        dynamicAdaptive: scores[2]
      },
      dimensionComparison: {}, // Would be populated with detailed dimension analysis
      insights: [
        `Best performing strategy: ${personaTypes[bestIndex]}`,
        `Score difference: ${Math.max(...scores) - Math.min(...scores)} points`
      ],
      recommendations: [
        'Consider the context-specific effectiveness of each approach',
        'Analyze patient readiness progression patterns'
      ]
    };
  }

  private broadcastMultiTherapistMessage(
    multiResponse: MultiTherapistResponse, 
    messages: MultiTherapistConversationMessage[]
  ): void {
    this.broadcastToClients({
      type: 'multi_therapist_message',
      data: {
        responses: {
          cbtOnly: {
            message: messages[0],
            thinking: {
              agent: 'therapist',
              content: messages[0].thinking,
              timestamp: messages[0].timestamp,
              personaType: 'cbt-only',
              personaName: messages[0].personaName
            }
          },
          miFixedPretreatment: {
            message: messages[1],
            thinking: {
              agent: 'therapist',
              content: messages[1].thinking,
              timestamp: messages[1].timestamp,
              personaType: 'mi-fixed-pretreatment',
              personaName: messages[1].personaName
            }
          },
          dynamicAdaptive: {
            message: messages[2],
            thinking: {
              agent: 'therapist',
              content: messages[2].thinking,
              timestamp: messages[2].timestamp,
              personaType: 'dynamic-adaptive',
              personaName: messages[2].personaName
            }
          }
        }
      },
      conversationId: this.context.id,
      timestamp: new Date().toISOString()
    });
  }

  private broadcastPatientMessage(message: MultiTherapistConversationMessage): void {
    this.broadcastToClients({
      type: 'patient_message',
      data: {
        message: {
          sender: message.sender,
          content: message.content,
          timestamp: message.timestamp
        },
        thinking: {
          agent: 'patient',
          content: message.thinking,
          timestamp: message.timestamp
        }
      },
      conversationId: this.context.id,
      timestamp: message.timestamp
    });
  }

  private broadcastToClients(message: any): void {
    console.log(`📢 Broadcasting to ${this.clients.length} clients:`, message.type);
    this.clients.forEach(client => {
      if (client.socket.readyState === 1) { // WebSocket.OPEN
        client.socket.send(JSON.stringify(message));
      }
    });
  }

  private handleError(message: string): void {
    console.error(`❌ ${message}`);
    this.broadcastToClients({
      type: 'error',
      message,
      timestamp: new Date().toISOString()
    });
  }

  // Client management methods
  addClient(client: WebSocketClient): void {
    this.clients.push(client);
    client.conversationId = this.context.id;
    console.log(`👥 Client ${client.id} added to multi-therapist conversation ${this.context.id}`);
  }

  removeClient(clientId: string): void {
    this.clients = this.clients.filter(c => c.id !== clientId);
    console.log(`👥 Client ${clientId} removed from multi-therapist conversation ${this.context.id}`);
  }

  getContext(): MultiTherapistConversationContext {
    return this.context;
  }

  isConversationActive(): boolean {
    return this.isActive;
  }
}
